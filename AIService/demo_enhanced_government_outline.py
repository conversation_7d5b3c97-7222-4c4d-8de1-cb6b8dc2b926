#!/usr/bin/env python3
"""
Demo: Enhanced Government Proposal Outline Generation

This script demonstrates the key improvements made to the government proposal outline system:
1. Enhanced prompt engineering with government evaluation criteria
2. JSON cleaning to handle LLM comments and formatting issues
3. Government-specific quality metrics and validation
4. Requirements traceability matrix generation
5. Compliance cross-referencing

This is a demonstration version that shows the enhanced features without complex dependencies.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from services.proposal.utilities import ProposalUtilities

class GovernmentOutlineEnhancementDemo:
    """Demonstration of enhanced government outline generation features"""
    
    def __init__(self):
        self.demo_mode = True
        self.enhancement_features = [
            "Government evaluation factor optimization",
            "Enhanced prompt engineering with FAR/DFARS compliance",
            "JSON cleaning for robust parsing",
            "Government-specific quality metrics",
            "Requirements traceability matrix",
            "Compliance cross-referencing",
            "No fallback mechanisms - quality or failure"
        ]
    
    def extract_and_clean_json(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract and clean JSON from LLM output, handling comments and formatting issues"""
        try:
            # First try the standard extraction
            outline = ProposalUtilities.extract_json_from_brackets(content)
            if outline is not None:
                return outline
        except:
            pass
        
        # If that fails, try cleaning the JSON
        json_str = ""
        try:
            # Find JSON content between braces
            start = content.find('{')
            end = content.rfind('}')
            
            if start == -1 or end == -1 or end <= start:
                print("❌ No valid JSON structure found in content")
                return None
            
            json_str = content[start:end+1]
            
            # Remove JavaScript-style comments (// comments)
            json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)
            
            # Remove multi-line comments (/* comments */)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
            
            # Remove trailing commas before closing braces/brackets
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            
            # Try to parse the cleaned JSON
            parsed_json = json.loads(json_str)
            print(f"✅ Successfully parsed JSON after cleaning")
            return parsed_json
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed even after cleaning: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error during JSON extraction: {e}")
            return None

    def create_enhanced_government_prompt(self, section_title: str, section_desc: str) -> str:
        """Create enhanced government-specific prompt"""
        return f'''
        **ROLE:** Senior Government Proposal Strategist with 20+ years of federal contracting experience.
        
        **MISSION:** Generate evaluation-optimized proposal outline for: {section_title}
        
        **GOVERNMENT EVALUATION CRITERIA:**
        - Technical approach and methodology (35% weight)
        - Management approach and quality control (25% weight)
        - Staffing qualifications and experience (25% weight)
        - Past performance and customer satisfaction (15% weight)
        
        **COMPLIANCE REQUIREMENTS:**
        - FAR 52.215-1 Instructions to Offerors
        - Section 508 Accessibility Standards
        - FISMA Security Requirements
        - NIST Cybersecurity Framework
        
        **SECTION DETAILS:**
        Title: {section_title}
        Description: {section_desc}
        
        **GENERATE GOVERNMENT-OPTIMIZED OUTLINE:**
        
        Create a detailed outline that maximizes government evaluation scores by including:
        
        1. **Evaluation-Focused Content Strategy**
        2. **Compliance Integration and Validation**
        3. **Quantitative Success Metrics and KPIs**
        4. **Risk Mitigation and Quality Assurance**
        5. **Government Terminology and Standards**
        
        **CRITICAL: Return ONLY valid JSON without comments, explanations, or additional text.**
        
        **JSON SCHEMA:**
        {{
            "title": "string",
            "content": "string (detailed government-optimized content guide)",
            "page_limit": number,
            "purpose": "string (evaluation-focused purpose)",
            "evaluation_factors": ["string"],
            "sow_task_mapping": ["string"],
            "compliance_requirements": ["string"],
            "success_metrics": ["string"],
            "risk_mitigation": ["string"],
            "government_terminology": ["string"],
            "competitive_advantages": ["string"],
            "quality_assurance": ["string"],
            "rfp_vector_db_query": "string",
            "client_vector_db_query": "string",
            "custom_prompt": "string",
            "references": "string",
            "image_descriptions": ["string"],
            "government_quality_score": number
        }}
        '''

    def simulate_enhanced_llm_response(self, section_title: str) -> str:
        """Simulate enhanced LLM response with government-specific content"""
        return f'''
{{
  "title": "{section_title}",
  "content": "This enhanced government proposal section demonstrates comprehensive understanding of federal acquisition requirements. The content includes specific methodologies aligned with government evaluation criteria, quantitative performance metrics, and proactive risk mitigation strategies. Technical approach emphasizes compliance with FAR/DFARS requirements while showcasing innovative solutions that provide measurable value to the government.",
  "page_limit": 15,
  "purpose": "Demonstrate technical excellence and compliance to maximize government evaluation scores",
  "evaluation_factors": ["technical_approach", "management_approach", "past_performance", "staffing_qualifications"],
  "sow_task_mapping": ["TASK 1: Technical Implementation and Integration", "TASK 2: Quality Assurance and Compliance Validation", "TASK 3: Performance Monitoring and Reporting"],
  "compliance_requirements": ["FAR 52.215-1 Instructions to Offerors", "Section 508 Accessibility Standards", "FISMA Security Requirements", "NIST SP 800-53 Security Controls"],
  "success_metrics": ["100% SOW task completion rate", "Zero critical security vulnerabilities", "95% customer satisfaction rating", "On-time delivery rate ≥ 98%"],
  "risk_mitigation": ["Technical complexity: Phased implementation with milestone reviews", "Resource constraints: Cross-trained backup personnel", "Security risks: Continuous monitoring and threat assessment"],
  "government_terminology": ["Contracting Officer (CO)", "Contracting Officer Representative (COR)", "Performance Work Statement (PWS)", "Contract Line Item Number (CLIN)"],
  "competitive_advantages": ["Proven government experience with similar agencies", "Security clearance-ready personnel", "Established quality management system", "Cost-effective innovative solutions"],
  "quality_assurance": ["ISO 9001:2015 quality management system", "Regular internal audits and reviews", "Customer feedback integration", "Continuous improvement processes"],
  "rfp_vector_db_query": "Find comprehensive government requirements for technical proposal sections including evaluation criteria and compliance standards",
  "client_vector_db_query": "Find government agency specific preferences, past contract performance data, and evaluation methodologies",
  "custom_prompt": "Generate detailed technical content optimized for government evaluation criteria with emphasis on compliance, quality, and measurable outcomes",
  "references": "Federal Acquisition Regulation (FAR), Defense Federal Acquisition Regulation Supplement (DFARS), NIST Cybersecurity Framework",
  "image_descriptions": ["Technical architecture diagram", "Compliance matrix table", "Risk mitigation flowchart"],
  "government_quality_score": 94  // Excellent government-optimized quality
}}
'''

    def calculate_enhancement_metrics(self, outline: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate enhancement metrics for the generated outline"""
        
        # Count enhanced features
        enhanced_features = 0
        feature_details = {}
        
        if outline.get("evaluation_factors"):
            enhanced_features += 1
            feature_details["evaluation_factors"] = len(outline["evaluation_factors"])
        
        if outline.get("compliance_requirements"):
            enhanced_features += 1
            feature_details["compliance_requirements"] = len(outline["compliance_requirements"])
        
        if outline.get("success_metrics"):
            enhanced_features += 1
            feature_details["success_metrics"] = len(outline["success_metrics"])
        
        if outline.get("risk_mitigation"):
            enhanced_features += 1
            feature_details["risk_mitigation"] = len(outline["risk_mitigation"])
        
        if outline.get("government_terminology"):
            enhanced_features += 1
            feature_details["government_terminology"] = len(outline["government_terminology"])
        
        if outline.get("competitive_advantages"):
            enhanced_features += 1
            feature_details["competitive_advantages"] = len(outline["competitive_advantages"])
        
        # Calculate quality score
        government_quality_score = outline.get("government_quality_score", 0)
        
        return {
            "enhanced_features_count": enhanced_features,
            "feature_details": feature_details,
            "government_quality_score": government_quality_score,
            "enhancement_grade": self.get_quality_grade(government_quality_score),
            "competitive_readiness": government_quality_score >= 85
        }

    def get_quality_grade(self, score: int) -> str:
        """Get quality grade based on government scoring"""
        if score >= 95:
            return "A+ (Exceptional - Highly Competitive)"
        elif score >= 90:
            return "A  (Excellent - Very Competitive)"
        elif score >= 85:
            return "B+ (Good - Competitive)"
        elif score >= 80:
            return "B  (Acceptable - Needs Improvement)"
        else:
            return "C  (Poor - Significant Issues)"

    async def demonstrate_enhanced_outline_generation(self, section_title: str, section_desc: str) -> Dict[str, Any]:
        """Demonstrate enhanced government outline generation"""
        
        print(f"\n🔧 DEMONSTRATING ENHANCED GENERATION FOR:")
        print(f"   Section: {section_title}")
        print(f"   Description: {section_desc}")
        
        # Step 1: Create enhanced government prompt
        print(f"\n📝 Step 1: Creating enhanced government-specific prompt...")
        enhanced_prompt = self.create_enhanced_government_prompt(section_title, section_desc)
        print(f"   ✅ Enhanced prompt created with government evaluation criteria")
        
        # Step 2: Simulate enhanced LLM response
        print(f"\n🤖 Step 2: Simulating enhanced LLM response...")
        llm_response = self.simulate_enhanced_llm_response(section_title)
        print(f"   ✅ Enhanced LLM response generated with government-specific content")
        
        # Step 3: Extract and clean JSON
        print(f"\n🧹 Step 3: Extracting and cleaning JSON...")
        outline = self.extract_and_clean_json(llm_response)
        
        if outline is None:
            raise ValueError(f"Failed to generate valid outline for section: {section_title}")
        
        # Step 4: Calculate enhancement metrics
        print(f"\n📊 Step 4: Calculating enhancement metrics...")
        enhancement_metrics = self.calculate_enhancement_metrics(outline)
        
        # Step 5: Create final enhanced result
        enhanced_result = {
            "outline": outline,
            "enhancement_metrics": enhancement_metrics,
            "enhancement_features_applied": self.enhancement_features,
            "generation_timestamp": datetime.now().isoformat(),
            "demo_mode": True
        }
        
        print(f"   ✅ Enhancement metrics calculated")
        print(f"   📈 Government Quality Score: {enhancement_metrics['government_quality_score']}%")
        print(f"   🏆 Quality Grade: {enhancement_metrics['enhancement_grade']}")
        print(f"   🎯 Enhanced Features: {enhancement_metrics['enhanced_features_count']}/6")
        print(f"   🏛️ Competitive Ready: {'Yes' if enhancement_metrics['competitive_readiness'] else 'No'}")
        
        return enhanced_result

async def main():
    """Main demonstration function"""
    
    print("🏛️  ENHANCED GOVERNMENT PROPOSAL OUTLINE GENERATION DEMO")
    print("="*70)
    print("This demo shows the key improvements made to the outline generation system:")
    
    demo = GovernmentOutlineEnhancementDemo()
    
    for i, feature in enumerate(demo.enhancement_features, 1):
        print(f"   {i}. {feature}")
    
    print("="*70)
    
    # Load table of contents for demonstration
    try:
        print(f"\n📖 Loading table of contents for demonstration...")
        table_of_contents_data = ProposalUtilities.read_json_from_file("table-of-contents.json")
        table_of_contents = table_of_contents_data.get("table_of_contents", [])
        print(f"   ✅ Loaded {len(table_of_contents)} sections")
        
        # Demonstrate with first section
        if table_of_contents:
            first_section = table_of_contents[0]
            section_title = first_section.get("title", "Sample Section")
            section_desc = first_section.get("description", "Sample description")
            
            result = await demo.demonstrate_enhanced_outline_generation(section_title, section_desc)
            
            # Save demonstration result
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"demo_enhanced_government_outline_{timestamp}.json"
            ProposalUtilities.save_json_to_file(result, output_filename)
            
            print(f"\n📄 Demo results saved to: {output_filename}")
            print(f"\n🎉 Enhanced Government Outline Generation Demo Completed Successfully!")
            print(f"\n💡 Key Takeaways:")
            print(f"   • JSON cleaning handles LLM comments and formatting issues")
            print(f"   • Government-specific prompts optimize for evaluation criteria")
            print(f"   • Enhanced features provide comprehensive proposal optimization")
            print(f"   • Quality scoring ensures competitive government proposals")
            
            return 0
        else:
            print("   ❌ No sections found in table of contents")
            return 1
            
    except FileNotFoundError:
        print("   ❌ table-of-contents.json not found - using sample data")
        
        # Use sample data for demonstration
        result = await demo.demonstrate_enhanced_outline_generation(
            "Tab A - Proposal Cover/Transmittal Letter",
            "Executive summary demonstrating understanding of government requirements"
        )
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"demo_enhanced_government_outline_{timestamp}.json"
        ProposalUtilities.save_json_to_file(result, output_filename)
        
        print(f"\n📄 Demo results saved to: {output_filename}")
        print(f"\n🎉 Enhanced Government Outline Generation Demo Completed Successfully!")
        
        return 0
        
    except Exception as e:
        print(f"   ❌ Demo failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
