# AI Service Environment Configuration
# This template will be processed to create the actual .env file

# Database Configuration - Kontratar Database
KONTRATAR_DB_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
KONTRATAR_DB_PORT=5432
KONTRATAR_DB_NAME=postgres
KONTRATAR_DB_USER=alpha
KONTRATAR_DB_PASSWORD=5t3r2i66123

# Database Configuration - Customer Database
CUSTOMER_DB_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
CUSTOMER_DB_PORT=5432
CUSTOMER_DB_NAME=postgres
CUSTOMER_DB_USER=alpha
CUSTOMER_DB_PASSWORD=5t3r2i66123

# AI Service Endpoints
AI_EMBEDDING_SERVER_NAME=
AI_EMBEDDING_SERVER_PORT=
AI_EMBEDDING_SERVER_PROTOCOL=
AI_IMAGE_SERVER_NAME=
AI_IMAGE_SERVER_PORT=
AI_IMAGE_SERVER_PROTOCOL=
AI_LLM_SERVER_NAME=
AI_LLM_SERVER_PORT=
AI_LLM_SERVER_PROTOCOL=

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=3011
DEBUG=false

# API Gateway and Auth Configuration
API_GATEWAY_PORT=
APP_PUBLIC_HOSTNAME=
AUTH_PORT=
AUTH_SECRET=
AUTH_TRUST_HOST=
AUTH_URL=
AWARD_REPOSITORY_PORT=
BASE_PORT=
CLIENT_ID=
CONTROLLER_HOST=
CONTROLLER_PORT=
DATABASE_URL=

# ChromaDB Configuration
CHROMADB_PROTOCOL=http
CHROMADB_SERVER_NAME=*************
CHROMADB_PORT_1=9001
CHROMADB_PORT_2=9002
CHROMADB_PORT_3=9003
CHROMADB_PORT_4=9004
CHROMADB_PORT_5=9005

# Legacy Kontratar DB fields (for compatibility)
DB_KONTRATAR_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
DB_KONTRATAR_NAME=postgres
DB_KONTRATAR_PASSWORD=5t3r2i66123
DB_KONTRATAR_PORT=5432
DB_KONTRATAR_USER=alpha

# External Services
DISCORD_WEBHOOK_URL=
DOMAIN=
ELK_PWD=
ELK_URL=
ELK_USERNAME=
EUREKA_HOSTNAME=
EUREKA_PORT=
FRONTEND_PORT=
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_CLIENT_SECRET=
INFORMATIONAL_SERVICE_PORT=

# Elasticsearch Configuration
ELASTICSEARCH_SERVER_HTTP_PROTOCOL=
ELASTICSEARCH_SERVER_NAME=
ELASTICSEARCH_SERVER_PASSWORD=
ELASTICSEARCH_SERVER_PORT=
ELASTICSEARCH_SERVER_USERNAME=

# Next.js Configuration
NEXT_PUBLIC_API_URL=
NEXT_PUBLIC_APP_MODE=
NEXT_PUBLIC_AUTH_URL=
NEXT_PUBLIC_GITHUB_TOKEN=
NEXT_PUBLIC_IS_URL=

# LangChain Configuration
LANGCHAIN_API_KEY=***************************************************
LANGCHAIN_PROJECT=ProposalGeneration
LANGCHAIN_TRACING_V2=true

# Scheduler Configuration
SCHEDULER_INTERVAL_SECONDS=60
