# AI Service Environment Configuration Template
# This template is processed by the enhanced token replacement utility
# Supports &token; syntax and conditional processing

# Production Environment Settings
DEBUG=false

# Database Configuration - Kontratar Database
KONTRATAR_DB_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
KONTRATAR_DB_PORT=5432
KONTRATAR_DB_NAME=postgres
KONTRATAR_DB_USER=alpha
KONTRATAR_DB_PASSWORD=5t3r2i66123

# Database Configuration - Customer Database
CUSTOMER_DB_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
CUSTOMER_DB_PORT=5432
CUSTOMER_DB_NAME=postgres
CUSTOMER_DB_USER=alpha
CUSTOMER_DB_PASSWORD=5t3r2i66123

# AI Service Endpoints (may be empty and will be commented out automatically)
# AI_EMBEDDING_SERVER_NAME=&ai_embedding_server_name;
# AI_EMBEDDING_SERVER_PORT=&ai_embedding_server_port;
# AI_EMBEDDING_SERVER_PROTOCOL=&ai_embedding_server_protocol;
# AI_IMAGE_SERVER_NAME=&ai_image_server_name;
# AI_IMAGE_SERVER_PORT=&ai_image_server_port;
# AI_IMAGE_SERVER_PROTOCOL=&ai_image_server_protocol;
# AI_LLM_SERVER_NAME=&ai_llm_server_name;
# AI_LLM_SERVER_PORT=&ai_llm_server_port;
# AI_LLM_SERVER_PROTOCOL=&ai_llm_server_protocol;

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=3011

# API Gateway and Auth Configuration
# API_GATEWAY_PORT=&api_gateway_port;
# APP_PUBLIC_HOSTNAME=&app_public_hostname;
# AUTH_PORT=&auth_port;
# AUTH_SECRET=&auth_secret;
# AUTH_TRUST_HOST=&auth_trust_host;
# AUTH_URL=&auth_url;
# AWARD_REPOSITORY_PORT=&award_repository_port;
# BASE_PORT=&base_port;
# CLIENT_ID=&client_id;
# CONTROLLER_HOST=&controller_host;
# CONTROLLER_PORT=&controller_port;
# DATABASE_URL=&database_url;

# ChromaDB Configuration
CHROMADB_PROTOCOL=http
CHROMADB_SERVER_NAME=*************
CHROMADB_PORT_1=9001
CHROMADB_PORT_2=9002
CHROMADB_PORT_3=9003
CHROMADB_PORT_4=9004
CHROMADB_PORT_5=9005

# Legacy Kontratar DB fields (for compatibility)
DB_KONTRATAR_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
DB_KONTRATAR_NAME=postgres
DB_KONTRATAR_PASSWORD=5t3r2i66123
DB_KONTRATAR_PORT=5432
DB_KONTRATAR_USER=alpha

# External Services (optional - will be commented if missing)
# DISCORD_WEBHOOK_URL=&discord_webhook_url;
# DOMAIN=&domain;
# ELK_PWD=&elk_pwd;
# ELK_URL=&elk_url;
# ELK_USERNAME=&elk_username;
# EUREKA_HOSTNAME=&eureka_hostname;
# EUREKA_PORT=&eureka_port;
# FRONTEND_PORT=&frontend_port;
# GOOGLE_AUTH_CLIENT_ID=&google_auth_client_id;
# GOOGLE_AUTH_CLIENT_SECRET=&google_auth_client_secret;
# INFORMATIONAL_SERVICE_PORT=&informational_service_port;

# Elasticsearch Configuration (optional - will be commented if missing)
# ELASTICSEARCH_SERVER_HTTP_PROTOCOL=&elasticsearch_server_http_protocol;
# ELASTICSEARCH_SERVER_NAME=&elasticsearch_server_name;
# ELASTICSEARCH_SERVER_PASSWORD=&elasticsearch_server_password;
# ELASTICSEARCH_SERVER_PORT=&elasticsearch_server_port;
# ELASTICSEARCH_SERVER_USERNAME=&elasticsearch_server_username;

# Next.js Configuration (optional - will be commented if missing)
# NEXT_PUBLIC_API_URL=&next_public_api_url;
# NEXT_PUBLIC_APP_MODE=&next_public_app_mode;
# NEXT_PUBLIC_AUTH_URL=&next_public_auth_url;
# NEXT_PUBLIC_GITHUB_TOKEN=&next_public_github_token;
# NEXT_PUBLIC_IS_URL=&next_public_is_url;

# LangChain Configuration
LANGCHAIN_API_KEY=***************************************************
LANGCHAIN_PROJECT=ProposalGeneration
LANGCHAIN_TRACING_V2=true

# Scheduler Configuration
SCHEDULER_INTERVAL_SECONDS=60


