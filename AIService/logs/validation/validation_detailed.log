2025-08-03 13:26:35 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:26:35 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:26:35 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:26:35 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:26:35 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:26:35 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:26:35 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:26:35 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:27:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:27:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:27:51 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:27:51 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:27:51 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:28:17 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:28:17 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:28:17 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:09 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:29:09 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:29:09 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:29:11 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:29:11 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:29:11 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:30:37 | INFO     | VALIDATION | Validation system loaded successfully
2025-08-03 13:30:37 | INFO     | VALIDATION | Starting outline generation script...
2025-08-03 13:30:37 | INFO     | VALIDATION | Loaded table of contents with 5 sections
2025-08-03 13:30:37 | INFO     | VALIDATION | Initializing ProposalOutlineService...
2025-08-03 13:30:38 | INFO     | VALIDATION | Generating outline...
2025-08-03 13:30:38 | INFO     | VALIDATION | Section title: Tab A - Proposal Cover/Transmittal Letter, Section Description: Standard cover letter for the proposal.
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:30:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:30:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:31:04 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:31:04 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:31:04 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:05 | INFO     | VALIDATION | Section title: Tab B - Factor 1 - Staffing & Key Personnel Qualifications, Section Description: Description of the approach to recruit, hire, retain, and develop qualified staff. Includes resumes of proposed Key Personnel and Tentative/Contingent Offer Letters.
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:32:05 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:32:05 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:32:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:32:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:32:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:39 | INFO     | VALIDATION | Section title: Recruitment, Hiring, and Retention Approach, Section Description: Details on the staffing approach.
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:33:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:33:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:33:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:33:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:33:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:54 | INFO     | VALIDATION | Section title: Certifications and Training Processes, Section Description: Description of personnel certifications and training.
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:34:54 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:34:54 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:34:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:34:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:34:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:43 | INFO     | VALIDATION | Section title: Resume of Proposed Key Personnel, Section Description: Resumes of key personnel (not included in 5-page limit).
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:35:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:35:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:35:44 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:35:44 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:35:44 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:55 | INFO     | VALIDATION | Section title: Tentative/Contingent Offer Letter, Section Description: Offer letters for key personnel (not included in 5-page limit).
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:36:55 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:36:55 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:36:56 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:36:56 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:36:56 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:38 | INFO     | VALIDATION | Section title: Tab C - Factor 2 - Management Approach, Section Description: Narrative describing the management approach for employee turnover, surge support, quality control, performance monitoring, and reporting.
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:37:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:37:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:37:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:37:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:37:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:38 | INFO     | VALIDATION | Section title: Employee Turnover and Solutions, Section Description: Description of approach to manage employee turnover.
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:38:38 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:38:38 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:38:40 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:38:40 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:38:40 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:36 | INFO     | VALIDATION | Section title: Surge Support Availability, Section Description: Details on surge support capabilities.
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:39:36 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:39:36 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:39:38 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:39:38 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:39:38 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:43 | INFO     | VALIDATION | Section title: Quality Control and Performance Monitoring, Section Description: Description of quality control processes.
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:40:43 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:40:43 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:40:45 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:40:45 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:40:45 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:44 | INFO     | VALIDATION | Section title: Tab D - Factor 3 - Technical Approach, Section Description: Demonstration of understanding of the scope, complexity, and level of effort for managing and supporting the Export Control Group (ECG). Includes expertise in relevant export control regulations and a feasible technical approach.
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:41:44 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:41:44 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:41:46 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:41:46 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:41:46 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:39 | INFO     | VALIDATION | Section title: TASK 1 – Program Management and Administration, Section Description: Details on program and task management support.
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:42:39 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:42:39 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:42:41 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:42:41 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:42:41 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:04 | INFO     | VALIDATION | Section title: TASK 2 – Information Management, Section Description: Details on collecting, analyzing, and storing compliance program information.
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:44:04 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:44:04 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:44:05 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:44:05 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:44:05 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:07 | INFO     | VALIDATION | Section title: TASK 3 – Program Compliance, Section Description: Details on facilitating DHS efforts to ensure export controls compliance.
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:45:07 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:45:07 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:45:09 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:45:09 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:45:09 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:13 | INFO     | VALIDATION | Section title: TASK 4 – Training and Outreach, Section Description: Details on providing technical and instructional support and products.
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:46:13 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:46:13 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:46:15 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:46:15 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:46:15 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:22 | INFO     | VALIDATION | Section title: TASK 5 – Regulatory Support, Section Description: Details on supporting DHS development and initiatives in export control compliance areas.
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:47:22 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:47:22 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:47:24 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:47:24 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:47:24 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:23 | INFO     | VALIDATION | Section title: TASK 6 – Optional – Surge, Section Description: Details on providing in-depth analysis of technologies with a homeland security nexus.
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:48:23 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:48:23 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:48:25 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:48:25 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:48:25 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:28 | INFO     | VALIDATION | Section title: Tab E - Factor 4 - Demonstrated Corporate Experience, Section Description: Up to three examples of relevant Federal Government experience demonstrating experience with SOW tasks 1-5.
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:49:28 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:49:28 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:49:30 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:49:30 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:49:30 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:27 | INFO     | VALIDATION | Section title: Experience Example 1, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:50:27 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:50:27 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:50:29 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:50:29 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:50:29 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:34 | INFO     | VALIDATION | Section title: Experience Example 2, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:51:34 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:51:34 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:51:35 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:51:35 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:51:35 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:37 | INFO     | VALIDATION | Section title: Experience Example 3, Section Description: Detailed description of relevant experience (max 2 pages).
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_opportunity_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning second part result: iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_tenant_id called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | base_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2', 'iRiYNgd8RC'] (type: <class 'list'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Returning first part result: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | extract_suffix called with collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC (type: <class 'str'>)
2025-08-03 13:52:37 | DEBUG    | VALIDATION | Split collection_name into parts: ['8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC'] (length: 1)
2025-08-03 13:52:37 | INFO     | VALIDATION | No suffix found in collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:52:39 | INFO     | VALIDATION | Building versioned_collection_name using collection_name: bdd60f6a67011073de513b05e3c4e94e and version: 19
2025-08-03 13:52:39 | INFO     | VALIDATION | Constructed versioned_collection_name: bdd60f6a67011073de513b05e3c4e94e.19, version_number: 19
2025-08-03 13:52:39 | INFO     | VALIDATION |  about to get latest collection name using chroma_url: http://*************:9002 for collection_name: 8d9e9729-f7bd-44a0-9cf1-777f532a2db2_iRiYNgd8RC
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generated successfully and saved to generated_outline_iRiYNgd8RC_8d9e9729-f7bd-44a0-9cf1-777f532a2db2.json
2025-08-03 13:53:39 | INFO     | VALIDATION | Generated outline contains 5 main sections
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 1: Tab A - Proposal Cover/Transmittal Letter (0 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 2: Tab B - Factor 1 - Staffing & Key Personnel Qualifications (4 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 3: Tab C - Factor 2 - Management Approach (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 4: Tab D - Factor 3 - Technical Approach (6 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION |   Section 5: Tab E - Factor 4 - Demonstrated Corporate Experience (3 subsections)
2025-08-03 13:53:39 | INFO     | VALIDATION | Outline generation completed successfully!
