#!/usr/bin/env python3
"""
Test script for JSON cleaning functionality
"""

import json
import re
from typing import Dict, Any, Optional

def extract_and_clean_json(content: str) -> Optional[Dict[str, Any]]:
    """
    Extract and clean JSON from LLM output, handling comments and formatting issues
    """
    
    # If that fails, try cleaning the JSON
    json_str = ""
    try:
        # Find JSON content between braces
        start = content.find('{')
        end = content.rfind('}')
        
        if start == -1 or end == -1 or end <= start:
            print("No valid JSON structure found in content")
            return None
        
        json_str = content[start:end+1]
        
        print(f"Original JSON string: {json_str[:300]}...")
        
        # Remove JavaScript-style comments (// comments)
        json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)
        
        # Remove multi-line comments (/* comments */)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        # Remove trailing commas before closing braces/brackets
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        print(f"Cleaned JSON string: {json_str[:300]}...")
        
        # Try to parse the cleaned JSON
        parsed_json = json.loads(json_str)
        print(f"Successfully parsed JSON after cleaning")
        return parsed_json
        
    except json.JSONDecodeError as e:
        print(f"JSON parsing failed even after cleaning: {e}")
        if 'json_str' in locals():
            print(f"Problematic JSON snippet: {json_str[:200]}...")
        return None
    except Exception as e:
        print(f"Unexpected error during JSON extraction: {e}")
        return None

def test_json_cleaning():
    """Test the JSON cleaning with the problematic output"""
    
    # This is the actual problematic JSON from the LLM
    test_content = '''
{
  "title": "DHS 70RSAT25R00000012 Technical Proposal",
  "content": "Detailed technical proposal content (as outlined above)",
  "page_limit": 50,
  "purpose": "Demonstrate technical understanding, management capabilities, and past performance to secure the DHS contract.",
  "evaluation_factors": ["Technical Approach", "Management Approach", "Past Performance", "Compliance & Security"],
  "sow_task_mapping": ["List of SOW tasks mapped to proposal sections"],
  "compliance_requirements": ["DHS security requirements, data protection standards, etc."],
  "success_metrics": ["KPIs for project performance, quality assurance metrics, etc."],
  "risk_mitigation": ["Identified risks and mitigation strategies"],
  "rfp_vector_db_query": "Retrieve relevant sections from the RFP document based on keywords and evaluation criteria.",
  "client_vector_db_query": "Retrieve information about DHS's security and training needs, past contracts, and preferred vendors.",
  "custom_prompt": "Generate detailed content for each section of the technical proposal, focusing on DHS's requirements and evaluation criteria.  Use a clear, concise, and persuasive writing style.",
  "references": "List of references used in the proposal.",
  "image_descriptions": ["Descriptions of any images or diagrams included in the proposal."],
  "government_quality_score": 85  // Estimated quality score (0-100)
}
'''
    
    print("Testing JSON cleaning functionality...")
    print("="*60)
    
    result = extract_and_clean_json(test_content)
    
    if result:
        print("✅ JSON cleaning successful!")
        print(f"Parsed JSON keys: {list(result.keys())}")
        print(f"Government quality score: {result.get('government_quality_score')}")
        print(f"Title: {result.get('title')}")
    else:
        print("❌ JSON cleaning failed!")

if __name__ == "__main__":
    test_json_cleaning()
