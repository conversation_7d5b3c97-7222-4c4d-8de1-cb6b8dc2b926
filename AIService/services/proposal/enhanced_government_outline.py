"""
Enhanced Government Proposal Outline Service

This service provides government-specific improvements to proposal outline generation:
- Enhanced prompt engineering with FAR/DFARS compliance
- Improved context retrieval with semantic ranking
- Government-specific quality metrics and validation
- Requirements traceability matrix generation
- Compliance cross-referencing
"""

import re
import asyncio
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# from services.proposal.outline import ProposalOutlineService  # Commented out to avoid SQLAlchemy dependency
from services.proposal.utilities import ProposalUtilities
from services.proposal.government_prompt_enhancer import GovernmentPromptEnhancer, ProposalSection
from database import get_kontratar_db
from loguru import logger

class GovernmentEvaluationFactor(Enum):
    """Government evaluation factors for proposals"""
    TECHNICAL_APPROACH = "technical_approach"
    MANAGEMENT_APPROACH = "management_approach" 
    PAST_PERFORMANCE = "past_performance"
    STAFFING_QUALIFICATIONS = "staffing_qualifications"
    PRICE = "price"
    SMALL_BUSINESS = "small_business"

@dataclass
class RequirementTraceability:
    """Tracks traceability between RFP requirements and outline sections"""
    requirement_id: str
    requirement_text: str
    sow_task: str
    outline_section: str
    coverage_level: str  # "full", "partial", "missing"
    evaluation_factor: GovernmentEvaluationFactor

@dataclass
class GovernmentQualityMetrics:
    """Quality metrics specific to government proposals"""
    compliance_score: float  # 0-100
    technical_depth_score: float  # 0-100
    traceability_score: float  # 0-100
    evaluation_alignment_score: float  # 0-100
    overall_government_score: float  # 0-100
    critical_gaps: List[str]
    recommendations: List[str]

class EnhancedGovernmentOutlineService:
    """Enhanced outline service with government-specific improvements"""

    def __init__(self,
                 embedding_api_url: str = "http://ai.kontratar.com:5000",
                 llm_api_url: str = "http://ai.kontratar.com:11434"):
        # Initialize without inheritance to avoid SQLAlchemy dependency
        self.embedding_api_url = embedding_api_url
        self.llm_api_url = llm_api_url
        self.traceability_matrix: List[RequirementTraceability] = []
        self.prompt_enhancer = GovernmentPromptEnhancer()

        # Note: This is a simplified version for testing enhanced features
        # In production, you would need to properly initialize ChromaService and LLM
        
    def generate_enhanced_chroma_query(self, section_title: str, section_desc: str, 
                                     query_type: str = "comprehensive") -> str:
        """
        Generate sophisticated ChromaDB queries for government proposals
        
        Args:
            section_title: The section title
            section_desc: Section description
            query_type: "requirements", "evaluation", "compliance", or "comprehensive"
        """
        base_query = f"Section: {section_title}. Description: {section_desc}"
        
        if query_type == "requirements":
            return f"""
            Find all specific requirements, deliverables, and performance standards for {section_title}.
            Include SOW tasks, evaluation criteria, submission requirements, and compliance standards.
            Focus on: {section_desc}
            """
        elif query_type == "evaluation":
            return f"""
            Find evaluation criteria, scoring methodologies, and assessment factors for {section_title}.
            Include technical evaluation factors, past performance requirements, and scoring weights.
            Focus on: {section_desc}
            """
        elif query_type == "compliance":
            return f"""
            Find compliance requirements, FAR clauses, security requirements, and regulatory standards for {section_title}.
            Include certification requirements, clearance levels, and mandatory compliance items.
            Focus on: {section_desc}
            """
        else:  # comprehensive
            return f"""
            Find comprehensive information for proposal section '{section_title}' including:
            - Specific requirements and deliverables
            - Evaluation criteria and scoring factors
            - Compliance and regulatory requirements
            - Technical specifications and performance standards
            - Submission format and page limits
            Context: {section_desc}
            """

    async def get_enhanced_context(self, section_title: str, section_desc: str,
                                 opportunity_id: str, tenant_id: str, source: str,
                                 max_chunks: int = 12) -> Dict[str, str]:
        """
        Get enhanced context using multiple query strategies
        
        Returns:
            Dict with different types of context: requirements, evaluation, compliance, comprehensive
        """
        context_types = {}
        
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            
            # Get different types of context
            for query_type in ["requirements", "evaluation", "compliance", "comprehensive"]:
                try:
                    query = self.generate_enhanced_chroma_query(section_title, section_desc, query_type)
                    
                    chunks = await asyncio.wait_for(
                        self.chroma_service.get_relevant_chunks(
                            db, collection_name, query, n_results=max_chunks//4
                        ),
                        timeout=30.0
                    )
                    
                    context_types[query_type] = "\n".join([
                        chunk.replace("\n", " ").replace("\t", " ") for chunk in chunks
                    ])
                    
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout getting {query_type} context for {section_title}")
                    context_types[query_type] = ""
                except Exception as e:
                    logger.error(f"Error getting {query_type} context: {e}")
                    context_types[query_type] = ""
            
            break
            
        return context_types

    def create_government_system_prompt(self) -> str:
        """Create enhanced system prompt for government proposals"""
        return '''
        **ROLE:** You are a Senior Government Proposal Strategist with 20+ years of experience winning federal contracts.
        
        **MISSION:** Generate detailed, compliant proposal outlines that maximize evaluation scores under government criteria.
        
        **GOVERNMENT EVALUATION EXPERTISE:**
        - FAR/DFARS compliance requirements
        - Technical evaluation factors and scoring methodologies  
        - Past performance evaluation criteria (CPARS, relevance, recency)
        - Management approach assessment (risk mitigation, quality control)
        - Staffing qualifications evaluation (certifications, clearances, experience)
        - Price evaluation methodologies (LPTA, best value, cost realism)
        
        **CRITICAL GOVERNMENT PROPOSAL REQUIREMENTS:**
        1. **Evaluation Factor Alignment:** Every outline element must align with specific evaluation factors
        2. **Requirements Traceability:** All SOW tasks must be explicitly addressed and cross-referenced
        3. **Compliance Integration:** Include FAR clauses, security requirements, and regulatory compliance
        4. **Quantitative Success Metrics:** Specify measurable outcomes and performance indicators
        5. **Risk Mitigation Focus:** Address potential risks and mitigation strategies
        6. **Government Terminology:** Use precise government acquisition terminology
        7. **Evaluation Scoring Optimization:** Structure content for maximum evaluation points
        
        **OUTLINE QUALITY STANDARDS:**
        - Technical depth appropriate for government evaluators
        - Specific methodologies, not generic approaches
        - Clear value propositions and differentiators
        - Compliance matrices and traceability tables
        - Quantified benefits and success metrics
        - Risk assessment and mitigation strategies
        
        **FORBIDDEN ELEMENTS:**
        - Generic commercial proposal language
        - Vague or non-specific commitments
        - Missing SOW task coverage
        - Inadequate compliance addressing
        - Weak technical methodologies
        - Insufficient past performance correlation
        '''

    def create_enhanced_user_prompt(self, section_title: str, section_desc: str,
                                  context_types: Dict[str, str], sow_tasks: Optional[List[str]] = None) -> str:
        """Create enhanced user prompt with government-specific requirements"""
        
        sow_context = ""
        if sow_tasks:
            sow_context = f"""
            **SOW TASKS TO ADDRESS:**
            {chr(10).join([f"- {task}" for task in sow_tasks])}
            """
        
        return f'''
        **SECTION:** {section_title}
        **DESCRIPTION:** {section_desc}
        
        {sow_context}
        
        **REQUIREMENTS CONTEXT:**
        {context_types.get("requirements", "")[:1500]}
        
        **EVALUATION CONTEXT:**
        {context_types.get("evaluation", "")[:1500]}
        
        **COMPLIANCE CONTEXT:**
        {context_types.get("compliance", "")[:1500]}
        
        **COMPREHENSIVE CONTEXT:**
        {context_types.get("comprehensive", "")[:2000]}
        
        **GENERATE ENHANCED GOVERNMENT OUTLINE:**
        
        Create a detailed outline that includes:
        
        1. **Government-Optimized Content Guide:**
           - Specific methodologies and technical approaches
           - Quantitative success metrics and KPIs
           - Risk mitigation strategies
           - Compliance requirements and validation methods
           - Value propositions and competitive differentiators
        
        2. **Evaluation Factor Alignment:**
           - Map content to specific evaluation criteria
           - Identify scoring opportunities
           - Address evaluation subfactors
        
        3. **Requirements Traceability:**
           - Cross-reference all SOW tasks
           - Map to RFP requirements
           - Ensure complete coverage
        
        4. **Government-Specific Elements:**
           - Compliance matrices (if required)
           - Performance measurement tables
           - Risk assessment frameworks
           - Quality assurance processes
        
        5. **Enhanced Custom Prompt:**
           - Step-by-step content generation instructions
           - Government evaluation criteria focus
           - Specific deliverable requirements
           - Quality validation checkpoints
        
        **CRITICAL: Return ONLY valid JSON. Do NOT include comments (// or /* */), explanations, or additional text. Numbers should be plain numbers without comments.**

        **JSON SCHEMA:**
        {{
            "title": "string",
            "content": "string (detailed government-optimized content guide)",
            "page_limit": number,
            "purpose": "string (evaluation-focused purpose)",
            "evaluation_factors": ["string"],
            "sow_task_mapping": ["string"],
            "compliance_requirements": ["string"],
            "success_metrics": ["string"],
            "risk_mitigation": ["string"],
            "rfp_vector_db_query": "string (enhanced multi-faceted query)",
            "client_vector_db_query": "string (government client-focused query)",
            "custom_prompt": "string (government-optimized generation prompt)",
            "references": "string",
            "image_descriptions": ["string"],
            "government_quality_score": number
        }}
        '''

    async def generate_enhanced_government_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate enhanced government proposal outline with improved quality and compliance
        """
        logger.info(f"Starting enhanced government outline generation for {opportunity_id}")

        # Initialize traceability matrix
        self.traceability_matrix = []

        async def enhanced_outline_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Generating enhanced outline for: {section_number} - {section_title}")

            # Get enhanced context using multiple query strategies
            context_types = await self.get_enhanced_context(
                section_title, section_desc, opportunity_id, tenant_id, source
            )

            # Extract SOW tasks from context
            sow_tasks = self.extract_sow_tasks_from_context(context_types)

            # Create enhanced prompts
            system_prompt = self.create_government_system_prompt()
            user_prompt = self.create_enhanced_user_prompt(
                section_title, section_desc, context_types, sow_tasks
            )

            # Generate outline with enhanced prompts
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            try:
                result = self.llm.invoke(messages)
                content = str(result.content)

                # Parse and validate the enhanced outline
                outline = self.extract_and_clean_json(content)

                if outline is None:
                    logger.error(f"Failed to parse outline for {section_title}")
                    logger.error(f"Raw LLM output: {content[:500]}...")
                    raise ValueError(f"Failed to generate valid outline for section: {section_title}")

                # Enhance outline with government-specific validation
                enhanced_outline = self.enhance_outline_with_government_features(
                    outline, section_title, section_desc, context_types, sow_tasks
                )

                # Update traceability matrix
                self.update_traceability_matrix(enhanced_outline, sow_tasks)

                # Process subsections recursively
                subsections = section.get("subsections", [])
                if subsections:
                    enhanced_outline["subsections"] = []
                    for subsection in subsections:
                        sub_outline = await enhanced_outline_for_section(subsection)
                        enhanced_outline["subsections"].append(sub_outline)

                return enhanced_outline

            except Exception as e:
                logger.error(f"Error generating enhanced outline for {section_title}: {e}")
                raise RuntimeError(f"Failed to generate outline for section {section_title}: {str(e)}")

        # Generate enhanced outlines for all sections
        enhanced_outlines = []
        for section in table_of_contents:
            outline = await enhanced_outline_for_section(section)
            enhanced_outlines.append(outline)

        # Calculate overall quality metrics
        quality_metrics = self.calculate_government_quality_metrics(enhanced_outlines)

        # Generate requirements traceability matrix
        traceability_matrix = self.generate_traceability_matrix()

        return {
            "outlines": enhanced_outlines,
            "government_quality_metrics": quality_metrics.__dict__,
            "requirements_traceability_matrix": traceability_matrix,
            "enhancement_summary": {
                "total_sections": len(enhanced_outlines),
                "avg_quality_score": quality_metrics.overall_government_score,
                "compliance_score": quality_metrics.compliance_score,
                "critical_gaps": quality_metrics.critical_gaps,
                "recommendations": quality_metrics.recommendations
            }
        }

    def extract_sow_tasks_from_context(self, context_types: Dict[str, str]) -> List[str]:
        """Extract SOW tasks from context using pattern matching"""
        sow_tasks = []

        # Combine all context for SOW task extraction
        combined_context = " ".join(context_types.values())

        # Pattern to match SOW tasks (TASK 1, Task 2, etc.)
        task_pattern = r'TASK\s+(\d+)[:\-–]\s*([^.]+(?:\.[^.]*)*)'
        matches = re.findall(task_pattern, combined_context, re.IGNORECASE)

        for task_num, task_desc in matches:
            sow_tasks.append(f"TASK {task_num}: {task_desc.strip()}")

        # Also look for numbered tasks without "TASK" prefix
        numbered_pattern = r'(\d+\.\d+)\s+([A-Z][^.]+(?:\.[^.]*)*)'
        numbered_matches = re.findall(numbered_pattern, combined_context)

        for task_num, task_desc in numbered_matches:
            if len(task_desc) > 20:  # Filter out short fragments
                sow_tasks.append(f"Section {task_num}: {task_desc.strip()}")

        return list(set(sow_tasks))  # Remove duplicates

    def identify_compliance_requirements(self, context_types: Dict[str, str]) -> List[str]:
        """Identify compliance requirements from context"""
        requirements = []
        compliance_context = context_types.get("compliance", "")

        # Common government compliance patterns
        compliance_patterns = [
            r'FAR\s+[\d.]+',
            r'DFARS\s+[\d.]+',
            r'Section\s+508',
            r'FISMA',
            r'NIST\s+SP\s+[\d-]+',
            r'Security\s+clearance',
            r'Controlled\s+Unclassified\s+Information',
            r'CUI',
            r'ITAR',
            r'EAR'
        ]

        for pattern in compliance_patterns:
            matches = re.findall(pattern, compliance_context, re.IGNORECASE)
            requirements.extend(matches)

        return list(set(requirements))

    def enhance_outline_with_government_features(
        self,
        outline: Dict[str, Any],
        section_title: str,
        section_desc: str,
        context_types: Dict[str, str],
        sow_tasks: List[str]
    ) -> Dict[str, Any]:
        """Enhance outline with government-specific features"""

        # Add government-specific fields if missing
        if "evaluation_factors" not in outline:
            outline["evaluation_factors"] = self.identify_evaluation_factors(section_title, context_types)

        if "sow_task_mapping" not in outline:
            outline["sow_task_mapping"] = self.map_sow_tasks(section_title, sow_tasks)

        if "compliance_requirements" not in outline:
            outline["compliance_requirements"] = self.identify_compliance_requirements(context_types)

        if "success_metrics" not in outline:
            outline["success_metrics"] = self.generate_success_metrics(section_title, section_desc)

        if "risk_mitigation" not in outline:
            outline["risk_mitigation"] = self.identify_risk_mitigation(section_title, section_desc)

        if "government_quality_score" not in outline:
            outline["government_quality_score"] = self.calculate_section_quality_score(outline)

        return outline

    def identify_evaluation_factors(self, section_title: str, context_types: Dict[str, str]) -> List[str]:
        """Identify relevant government evaluation factors for a section"""
        factors = []
        title_lower = section_title.lower()

        if any(word in title_lower for word in ["technical", "approach", "methodology"]):
            factors.append("technical_approach")
        if any(word in title_lower for word in ["management", "project", "quality"]):
            factors.append("management_approach")
        if any(word in title_lower for word in ["staff", "personnel", "team", "qualifications"]):
            factors.append("staffing_qualifications")
        if any(word in title_lower for word in ["experience", "performance", "past"]):
            factors.append("past_performance")
        if any(word in title_lower for word in ["price", "cost", "pricing"]):
            factors.append("price")
        if any(word in title_lower for word in ["small", "business", "subcontract"]):
            factors.append("small_business")

        return factors if factors else ["technical_approach"]

    def map_sow_tasks(self, section_title: str, sow_tasks: List[str]) -> List[str]:
        """Map SOW tasks to a specific section"""
        mapped_tasks = []
        title_keywords = section_title.lower().split()

        for task in sow_tasks:
            task_lower = task.lower()
            # Simple keyword matching - could be enhanced with NLP
            if any(keyword in task_lower for keyword in title_keywords if len(keyword) > 3):
                mapped_tasks.append(task)

        return mapped_tasks

    def generate_success_metrics(self, section_title: str, section_desc: str) -> List[str]:
        """Generate success metrics based on section type"""
        metrics = []
        title_lower = section_title.lower()

        if "technical" in title_lower:
            metrics.extend([
                "100% SOW task completion rate",
                "Zero critical technical defects",
                "On-time deliverable submission rate ≥ 95%"
            ])

        if "management" in title_lower:
            metrics.extend([
                "Project schedule adherence ≥ 98%",
                "Budget variance ≤ 2%",
                "Risk mitigation effectiveness ≥ 90%"
            ])

        if "staff" in title_lower or "personnel" in title_lower:
            metrics.extend([
                "Staff retention rate ≥ 95%",
                "Average time to fill positions ≤ 30 days",
                "Personnel certification compliance 100%"
            ])

        if "experience" in title_lower:
            metrics.extend([
                "Relevant experience coverage ≥ 90%",
                "Customer satisfaction score ≥ 4.5/5.0",
                "Contract performance rating: Exceptional"
            ])

        # Use section_desc for additional context
        if "quality" in section_desc.lower():
            metrics.append("Quality metrics compliance 100%")

        return metrics if metrics else ["Performance standard compliance 100%"]

    def identify_risk_mitigation(self, section_title: str, section_desc: str) -> List[str]:
        """Identify risk mitigation strategies"""
        risks = []
        title_lower = section_title.lower()

        if "technical" in title_lower:
            risks.extend([
                "Technical complexity: Phased implementation approach",
                "Technology obsolescence: Regular technology refresh cycles",
                "Integration challenges: Comprehensive testing protocols"
            ])

        if "staff" in title_lower:
            risks.extend([
                "Personnel turnover: Competitive retention packages",
                "Skill gaps: Continuous training programs",
                "Security clearance delays: Maintain cleared personnel pool"
            ])

        if "management" in title_lower:
            risks.extend([
                "Schedule delays: Buffer time allocation",
                "Scope creep: Change control procedures",
                "Communication gaps: Regular stakeholder meetings"
            ])

        # Use section_desc for additional context
        if "compliance" in section_desc.lower():
            risks.append("Compliance violations: Regular audit procedures")

        return risks if risks else ["Standard risk management protocols"]

    def calculate_section_quality_score(self, outline: Dict[str, Any]) -> int:
        """Calculate quality score for a section outline"""
        score = 50  # Base score

        # Check for government-specific enhancements
        if outline.get("evaluation_factors"):
            score += 10
        if outline.get("sow_task_mapping"):
            score += 15
        if outline.get("compliance_requirements"):
            score += 10
        if outline.get("success_metrics"):
            score += 10
        if outline.get("risk_mitigation"):
            score += 5

        # Check content quality
        content = outline.get("content", "")
        if len(content) > 500:
            score += 10
        if "methodology" in content.lower():
            score += 5
        if "compliance" in content.lower():
            score += 5

        return min(score, 100)

    def update_traceability_matrix(self, outline: Dict[str, Any], sow_tasks: List[str]) -> None:
        """Update the requirements traceability matrix"""
        section_title = outline.get("title", "")
        sow_mapping = outline.get("sow_task_mapping", [])

        for task in sow_mapping:
            traceability = RequirementTraceability(
                requirement_id=f"SOW-{len(self.traceability_matrix) + 1}",
                requirement_text=task,
                sow_task=task,
                outline_section=section_title,
                coverage_level="full",
                evaluation_factor=GovernmentEvaluationFactor.TECHNICAL_APPROACH
            )
            self.traceability_matrix.append(traceability)

    def calculate_government_quality_metrics(self, outlines: List[Dict[str, Any]]) -> GovernmentQualityMetrics:
        """Calculate overall government quality metrics"""
        total_sections = len(outlines)
        if total_sections == 0:
            return GovernmentQualityMetrics(0, 0, 0, 0, 0, [], [])

        # Calculate scores
        quality_scores = [outline.get("government_quality_score", 50) for outline in outlines]
        avg_quality = sum(quality_scores) / len(quality_scores)

        compliance_sections = sum(1 for outline in outlines if outline.get("compliance_requirements"))
        compliance_score = (compliance_sections / total_sections) * 100

        traceability_sections = sum(1 for outline in outlines if outline.get("sow_task_mapping"))
        traceability_score = (traceability_sections / total_sections) * 100

        evaluation_sections = sum(1 for outline in outlines if outline.get("evaluation_factors"))
        evaluation_score = (evaluation_sections / total_sections) * 100

        overall_score = (avg_quality + compliance_score + traceability_score + evaluation_score) / 4

        # Identify gaps
        critical_gaps = []
        if compliance_score < 80:
            critical_gaps.append("Insufficient compliance coverage")
        if traceability_score < 90:
            critical_gaps.append("Incomplete SOW task traceability")
        if avg_quality < 70:
            critical_gaps.append("Low content quality scores")

        # Generate recommendations
        recommendations = []
        if overall_score < 80:
            recommendations.append("Enhance technical depth and specificity")
        if compliance_score < 90:
            recommendations.append("Add more compliance requirements mapping")
        if traceability_score < 95:
            recommendations.append("Ensure complete SOW task coverage")

        return GovernmentQualityMetrics(
            compliance_score=compliance_score,
            technical_depth_score=avg_quality,
            traceability_score=traceability_score,
            evaluation_alignment_score=evaluation_score,
            overall_government_score=overall_score,
            critical_gaps=critical_gaps,
            recommendations=recommendations
        )

    def generate_traceability_matrix(self) -> List[Dict[str, Any]]:
        """Generate requirements traceability matrix"""
        return [
            {
                "requirement_id": trace.requirement_id,
                "requirement_text": trace.requirement_text,
                "sow_task": trace.sow_task,
                "outline_section": trace.outline_section,
                "coverage_level": trace.coverage_level,
                "evaluation_factor": trace.evaluation_factor.value
            }
            for trace in self.traceability_matrix
        ]

    def extract_and_clean_json(self, content: str) -> Optional[Dict[str, Any]]:
        """
        Extract and clean JSON from LLM output, handling comments and formatting issues
        """
        import json
        import re

        try:
            # First try the standard extraction
            outline = ProposalUtilities.extract_json_from_brackets(content)
            if outline is not None:
                return outline
        except:
            pass

        # If that fails, try cleaning the JSON
        json_str = ""
        try:
            # Find JSON content between braces
            start = content.find('{')
            end = content.rfind('}')

            if start == -1 or end == -1 or end <= start:
                logger.error("No valid JSON structure found in content")
                return None

            json_str = content[start:end+1]

            # Remove JavaScript-style comments (// comments)
            json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)

            # Remove multi-line comments (/* comments */)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

            # Remove trailing commas before closing braces/brackets
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            logger.info(f"Cleaned JSON string: {json_str[:300]}...")

            # Try to parse the cleaned JSON
            parsed_json = json.loads(json_str)
            logger.info(f"Successfully parsed JSON after cleaning for content length: {len(content)}")
            return parsed_json

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed even after cleaning: {e}")
            if 'json_str' in locals():
                logger.error(f"Problematic JSON snippet: {json_str[:200]}...")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during JSON extraction: {e}")
            return None
