"""
Government Proposal Prompt Enhancement Service

This service provides specialized prompt engineering for government proposals:
- FAR/DFARS compliance integration
- Evaluation factor optimization
- Government-specific terminology
- Quality scoring criteria
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass

class ProposalSection(Enum):
    """Government proposal section types"""
    COVER_LETTER = "cover_letter"
    TECHNICAL_APPROACH = "technical_approach"
    MANAGEMENT_APPROACH = "management_approach"
    STAFFING_QUALIFICATIONS = "staffing_qualifications"
    PAST_PERFORMANCE = "past_performance"
    PRICING = "pricing"
    SMALL_BUSINESS = "small_business"

@dataclass
class GovernmentPromptTemplate:
    """Template for government-specific prompts"""
    section_type: ProposalSection
    evaluation_weight: float
    compliance_requirements: List[str]
    evaluation_criteria: List[str]
    quality_indicators: List[str]
    risk_factors: List[str]

class GovernmentPromptEnhancer:
    """Enhanced prompt engineering for government proposals"""
    
    def __init__(self):
        self.prompt_templates = self._initialize_prompt_templates()
        self.government_terminology = self._load_government_terminology()
        self.evaluation_frameworks = self._load_evaluation_frameworks()
    
    def _initialize_prompt_templates(self) -> Dict[ProposalSection, GovernmentPromptTemplate]:
        """Initialize government-specific prompt templates"""
        return {
            ProposalSection.TECHNICAL_APPROACH: GovernmentPromptTemplate(
                section_type=ProposalSection.TECHNICAL_APPROACH,
                evaluation_weight=0.35,
                compliance_requirements=[
                    "FAR 52.215-1 Instructions to Offerors",
                    "Section 508 Accessibility",
                    "FISMA Security Requirements",
                    "NIST Cybersecurity Framework"
                ],
                evaluation_criteria=[
                    "Understanding of requirements",
                    "Technical feasibility",
                    "Innovation and efficiency",
                    "Risk mitigation approach",
                    "Quality assurance methods"
                ],
                quality_indicators=[
                    "Specific methodologies described",
                    "Quantitative success metrics provided",
                    "Risk mitigation strategies detailed",
                    "Compliance requirements addressed",
                    "Technical depth appropriate for evaluators"
                ],
                risk_factors=[
                    "Technical complexity underestimated",
                    "Integration challenges not addressed",
                    "Performance metrics too vague",
                    "Compliance gaps identified"
                ]
            ),
            ProposalSection.MANAGEMENT_APPROACH: GovernmentPromptTemplate(
                section_type=ProposalSection.MANAGEMENT_APPROACH,
                evaluation_weight=0.25,
                compliance_requirements=[
                    "FAR 52.246-1 Contractor Quality Assurance",
                    "Project Management Institute standards",
                    "Government reporting requirements"
                ],
                evaluation_criteria=[
                    "Project management methodology",
                    "Quality control processes",
                    "Risk management approach",
                    "Communication protocols",
                    "Performance monitoring"
                ],
                quality_indicators=[
                    "Proven project management framework",
                    "Clear quality control procedures",
                    "Comprehensive risk management",
                    "Regular reporting mechanisms",
                    "Stakeholder engagement plan"
                ],
                risk_factors=[
                    "Project management approach too generic",
                    "Quality control insufficient",
                    "Risk management inadequate",
                    "Communication gaps potential"
                ]
            ),
            ProposalSection.STAFFING_QUALIFICATIONS: GovernmentPromptTemplate(
                section_type=ProposalSection.STAFFING_QUALIFICATIONS,
                evaluation_weight=0.25,
                compliance_requirements=[
                    "Security clearance requirements",
                    "Professional certification standards",
                    "Equal opportunity employment",
                    "Wage determination compliance"
                ],
                evaluation_criteria=[
                    "Key personnel qualifications",
                    "Staffing approach and retention",
                    "Training and development",
                    "Organizational structure",
                    "Succession planning"
                ],
                quality_indicators=[
                    "Relevant experience demonstrated",
                    "Appropriate certifications held",
                    "Clear roles and responsibilities",
                    "Retention strategies outlined",
                    "Contingency staffing planned"
                ],
                risk_factors=[
                    "Key personnel unavailable",
                    "Insufficient qualifications",
                    "High turnover potential",
                    "Clearance processing delays"
                ]
            ),
            ProposalSection.PAST_PERFORMANCE: GovernmentPromptTemplate(
                section_type=ProposalSection.PAST_PERFORMANCE,
                evaluation_weight=0.15,
                compliance_requirements=[
                    "FAR 52.215-1 Past Performance Information",
                    "CPARS reporting accuracy",
                    "Reference verification requirements"
                ],
                evaluation_criteria=[
                    "Relevance to current requirement",
                    "Recency of experience",
                    "Contract performance quality",
                    "Customer satisfaction",
                    "Problem resolution capability"
                ],
                quality_indicators=[
                    "Directly relevant experience",
                    "Recent contract performance",
                    "Excellent CPARS ratings",
                    "Positive customer feedback",
                    "Successful problem resolution"
                ],
                risk_factors=[
                    "Limited relevant experience",
                    "Poor past performance ratings",
                    "Outdated experience examples",
                    "Unverifiable references"
                ]
            )
        }
    
    def _load_government_terminology(self) -> Dict[str, str]:
        """Load government-specific terminology mappings"""
        return {
            "client": "Government",
            "customer": "Government",
            "company": "Contractor/Offeror",
            "vendor": "Contractor",
            "project": "Contract/Task Order",
            "deliverable": "Contract Deliverable",
            "milestone": "Performance Milestone",
            "requirement": "Contract Requirement",
            "specification": "Technical Specification",
            "proposal": "Technical Proposal",
            "bid": "Offer/Proposal",
            "contract": "Government Contract",
            "statement of work": "SOW",
            "performance work statement": "PWS",
            "request for proposal": "RFP",
            "contracting officer": "CO",
            "contracting officer representative": "COR",
            "government technical monitor": "GTM",
            "federal acquisition regulation": "FAR",
            "defense federal acquisition regulation": "DFARS"
        }
    
    def _load_evaluation_frameworks(self) -> Dict[str, Dict[str, Any]]:
        """Load government evaluation frameworks"""
        return {
            "best_value": {
                "description": "Best value evaluation considering technical merit and price",
                "factors": ["technical_approach", "past_performance", "price"],
                "emphasis": "Technical superiority with reasonable price"
            },
            "lpta": {
                "description": "Lowest Price Technically Acceptable",
                "factors": ["technical_acceptability", "price"],
                "emphasis": "Minimum technical standards with lowest price"
            },
            "technical_merit": {
                "description": "Technical merit evaluation with price consideration",
                "factors": ["technical_approach", "management_approach", "staffing", "price"],
                "emphasis": "Technical excellence and capability demonstration"
            }
        }
    
    def enhance_system_prompt_for_government(self, section_type: ProposalSection, 
                                           evaluation_method: str = "best_value") -> str:
        """
        Create enhanced system prompt for government proposals
        
        Args:
            section_type: Type of proposal section
            evaluation_method: Government evaluation method (best_value, lpta, technical_merit)
        """
        template = self.prompt_templates.get(section_type)
        if not template:
            raise ValueError(f"No template found for section type: {section_type}")
        
        evaluation_framework = self.evaluation_frameworks.get(evaluation_method, 
                                                            self.evaluation_frameworks["best_value"])
        
        return f"""
        **ROLE:** Senior Government Proposal Strategist specializing in {section_type.value.replace('_', ' ').title()}
        
        **EVALUATION CONTEXT:**
        - Evaluation Method: {evaluation_framework['description']}
        - Section Weight: {template.evaluation_weight:.0%} of total evaluation
        - Evaluation Emphasis: {evaluation_framework['emphasis']}
        
        **COMPLIANCE REQUIREMENTS:**
        {chr(10).join([f"- {req}" for req in template.compliance_requirements])}
        
        **EVALUATION CRITERIA (Government evaluators will assess):**
        {chr(10).join([f"- {criteria}" for criteria in template.evaluation_criteria])}
        
        **QUALITY INDICATORS (Must demonstrate):**
        {chr(10).join([f"- {indicator}" for indicator in template.quality_indicators])}
        
        **RISK MITIGATION (Avoid these pitfalls):**
        {chr(10).join([f"- {risk}" for risk in template.risk_factors])}
        
        **GOVERNMENT PROPOSAL STANDARDS:**
        1. **Evaluation Optimization:** Structure content to maximize scoring under government criteria
        2. **Compliance Integration:** Address all regulatory and contractual requirements
        3. **Technical Precision:** Use specific methodologies, not generic approaches
        4. **Quantitative Evidence:** Provide measurable outcomes and success metrics
        5. **Risk Awareness:** Proactively address potential challenges and mitigation
        6. **Government Terminology:** Use precise acquisition and technical terminology
        7. **Evaluator Focus:** Write for government technical evaluators and contracting officers
        
        **CRITICAL SUCCESS FACTORS:**
        - Demonstrate clear understanding of government requirements
        - Provide specific, actionable methodologies
        - Show quantifiable benefits and value proposition
        - Address compliance requirements comprehensively
        - Mitigate identified risks proactively
        - Use government-appropriate language and terminology
        
        **OUTPUT REQUIREMENTS:**
        - Government-compliant proposal content
        - Evaluation-optimized structure and messaging
        - Comprehensive requirement coverage
        - Professional government proposal tone
        - Technical depth appropriate for evaluators
        """
    
    def enhance_user_prompt_for_government(self, section_title: str, section_desc: str,
                                         sow_tasks: List[str], evaluation_criteria: List[str],
                                         compliance_requirements: List[str]) -> str:
        """
        Create enhanced user prompt with government-specific requirements
        """
        return f"""
        **GOVERNMENT PROPOSAL SECTION:** {section_title}
        **SECTION DESCRIPTION:** {section_desc}
        
        **SOW TASKS TO ADDRESS:**
        {chr(10).join([f"- {task}" for task in sow_tasks]) if sow_tasks else "- No specific SOW tasks identified"}
        
        **EVALUATION CRITERIA:**
        {chr(10).join([f"- {criteria}" for criteria in evaluation_criteria]) if evaluation_criteria else "- Standard government evaluation criteria apply"}
        
        **COMPLIANCE REQUIREMENTS:**
        {chr(10).join([f"- {req}" for req in compliance_requirements]) if compliance_requirements else "- Standard FAR compliance requirements"}
        
        **GENERATE GOVERNMENT-OPTIMIZED OUTLINE:**
        
        Create a detailed outline that maximizes government evaluation scores by including:
        
        1. **Evaluation-Focused Content Strategy:**
           - Address each evaluation criterion explicitly
           - Provide quantitative success metrics
           - Demonstrate technical depth and understanding
           - Show clear value proposition to government
        
        2. **Compliance Integration:**
           - Map all compliance requirements to content
           - Address regulatory standards comprehensively
           - Include necessary certifications and standards
        
        3. **Government-Specific Elements:**
           - Use proper government terminology
           - Reference applicable FAR/DFARS clauses
           - Include required tables and matrices
           - Address government evaluation methodology
        
        4. **Risk Mitigation Framework:**
           - Identify potential risks and challenges
           - Provide specific mitigation strategies
           - Demonstrate proactive risk management
        
        5. **Quality Assurance Standards:**
           - Define measurable quality metrics
           - Establish performance monitoring procedures
           - Include validation and verification methods
        
        **CRITICAL: Return ONLY valid JSON without comments, explanations, or additional text.**

        **ENHANCED JSON SCHEMA:**
        {{
            "title": "string",
            "content": "string",
            "page_limit": number,
            "purpose": "string",
            "evaluation_factors": ["string"],
            "evaluation_criteria_mapping": ["string"],
            "sow_task_mapping": ["string"],
            "compliance_requirements": ["string"],
            "success_metrics": ["string"],
            "risk_mitigation": ["string"],
            "government_terminology": ["string"],
            "quality_assurance": ["string"],
            "competitive_advantages": ["string"],
            "rfp_vector_db_query": "string",
            "client_vector_db_query": "string",
            "custom_prompt": "string",
            "references": "string",
            "image_descriptions": ["string"],
            "government_quality_score": number,
            "evaluation_optimization_notes": "string"
        }}
        """
    
    def get_government_terminology_replacements(self, text: str) -> str:
        """Replace commercial terms with government terminology"""
        result = text
        for commercial_term, government_term in self.government_terminology.items():
            # Case-insensitive replacement while preserving case
            import re
            pattern = re.compile(re.escape(commercial_term), re.IGNORECASE)
            result = pattern.sub(government_term, result)
        return result
