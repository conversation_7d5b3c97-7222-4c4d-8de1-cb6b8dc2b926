#!/usr/bin/env python3
"""
Simplified Enhanced Government Proposal Outline Generator

This is a standalone version that doesn't inherit from the base ProposalOutlineService
to avoid SQLAlchemy dependency issues while testing the enhanced functionality.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from services.proposal.utilities import ProposalUtilities

class SimpleEnhancedGovernmentOutlineService:
    """Simplified enhanced outline service for testing"""
    
    def __init__(self):
        self.test_mode = True
    
    def extract_and_clean_json(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract and clean JSON from LLM output, handling comments and formatting issues"""
        import json
        import re
        
        try:
            # First try the standard extraction
            outline = ProposalUtilities.extract_json_from_brackets(content)
            if outline is not None:
                return outline
        except:
            pass
        
        # If that fails, try cleaning the JSON
        json_str = ""
        try:
            # Find JSON content between braces
            start = content.find('{')
            end = content.rfind('}')
            
            if start == -1 or end == -1 or end <= start:
                print("No valid JSON structure found in content")
                return None
            
            json_str = content[start:end+1]
            
            # Remove JavaScript-style comments (// comments)
            json_str = re.sub(r'//.*$', '', json_str, flags=re.MULTILINE)
            
            # Remove multi-line comments (/* comments */)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
            
            # Remove trailing commas before closing braces/brackets
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            
            print(f"Cleaned JSON string: {json_str[:300]}...")
            
            # Try to parse the cleaned JSON
            parsed_json = json.loads(json_str)
            print(f"✅ Successfully parsed JSON after cleaning")
            return parsed_json
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed even after cleaning: {e}")
            if 'json_str' in locals():
                print(f"Problematic JSON snippet: {json_str[:200]}...")
            return None
        except Exception as e:
            print(f"❌ Unexpected error during JSON extraction: {e}")
            return None

    def create_government_system_prompt(self) -> str:
        """Create enhanced system prompt for government proposals"""
        return '''
        **ROLE:** You are a Senior Government Proposal Strategist with 20+ years of experience winning federal contracts.
        
        **MISSION:** Generate detailed, compliant proposal outlines that maximize evaluation scores under government criteria.
        
        **GOVERNMENT EVALUATION EXPERTISE:**
        - FAR/DFARS compliance requirements
        - Technical evaluation factors and scoring methodologies  
        - Past performance evaluation criteria (CPARS, relevance, recency)
        - Management approach assessment (risk mitigation, quality control)
        - Staffing qualifications evaluation (certifications, clearances, experience)
        - Price evaluation methodologies (LPTA, best value, cost realism)
        
        **CRITICAL GOVERNMENT PROPOSAL REQUIREMENTS:**
        1. **Evaluation Factor Alignment:** Every outline element must align with specific evaluation factors
        2. **Requirements Traceability:** All SOW tasks must be explicitly addressed and cross-referenced
        3. **Compliance Integration:** Include FAR clauses, security requirements, and regulatory compliance
        4. **Quantitative Success Metrics:** Specify measurable outcomes and performance indicators
        5. **Risk Mitigation Focus:** Address potential risks and mitigation strategies
        6. **Government Terminology:** Use precise government acquisition terminology
        7. **Evaluation Scoring Optimization:** Structure content for maximum evaluation points
        
        **OUTLINE QUALITY STANDARDS:**
        - Technical depth appropriate for government evaluators
        - Specific methodologies, not generic approaches
        - Clear value propositions and differentiators
        - Compliance matrices and traceability tables
        - Quantified benefits and success metrics
        - Risk assessment and mitigation strategies
        
        **FORBIDDEN ELEMENTS:**
        - Generic commercial proposal language
        - Vague or non-specific commitments
        - Missing SOW task coverage
        - Inadequate compliance addressing
        - Weak technical methodologies
        - Insufficient past performance correlation
        '''

    def create_enhanced_user_prompt(self, section_title: str, section_desc: str) -> str:
        """Create enhanced user prompt with government-specific requirements"""
        
        return f'''
        **SECTION:** {section_title}
        **DESCRIPTION:** {section_desc}
        
        **GENERATE ENHANCED GOVERNMENT OUTLINE:**
        
        Create a detailed outline that includes:
        
        1. **Government-Optimized Content Guide:**
           - Specific methodologies and technical approaches
           - Quantitative success metrics and KPIs
           - Risk mitigation strategies
           - Compliance requirements and validation methods
           - Value propositions and competitive differentiators
        
        2. **Evaluation Factor Alignment:**
           - Map content to specific evaluation criteria
           - Identify scoring opportunities
           - Address evaluation subfactors
        
        3. **Requirements Traceability:**
           - Cross-reference all SOW tasks
           - Map to RFP requirements
           - Ensure complete coverage
        
        4. **Government-Specific Elements:**
           - Compliance matrices (if required)
           - Performance measurement tables
           - Risk assessment frameworks
           - Quality assurance processes
        
        5. **Enhanced Custom Prompt:**
           - Step-by-step content generation instructions
           - Government evaluation criteria focus
           - Specific deliverable requirements
           - Quality validation checkpoints
        
        **CRITICAL: Return ONLY valid JSON. Do NOT include comments (// or /* */), explanations, or additional text. Numbers should be plain numbers without comments.**
        
        **JSON SCHEMA:**
        {{
            "title": "string",
            "content": "string (detailed government-optimized content guide)",
            "page_limit": number,
            "purpose": "string (evaluation-focused purpose)",
            "evaluation_factors": ["string"],
            "sow_task_mapping": ["string"],
            "compliance_requirements": ["string"],
            "success_metrics": ["string"],
            "risk_mitigation": ["string"],
            "rfp_vector_db_query": "string (enhanced multi-faceted query)",
            "client_vector_db_query": "string (government client-focused query)",
            "custom_prompt": "string (government-optimized generation prompt)",
            "references": "string",
            "image_descriptions": ["string"],
            "government_quality_score": number
        }}
        '''

    async def test_enhanced_outline_generation(self, section_title: str, section_desc: str) -> Dict[str, Any]:
        """Test enhanced outline generation for a single section"""
        
        print(f"🔧 Testing enhanced outline generation for: {section_title}")
        
        # Create enhanced prompts
        system_prompt = self.create_government_system_prompt()
        user_prompt = self.create_enhanced_user_prompt(section_title, section_desc)
        
        # Simulate LLM response with the problematic JSON (including comment)
        simulated_llm_response = '''
{
  "title": "Enhanced Government Proposal Section",
  "content": "This section demonstrates enhanced government proposal generation with specific methodologies, quantitative success metrics, risk mitigation strategies, and compliance requirements. The content is optimized for government evaluation criteria and includes technical depth appropriate for federal evaluators.",
  "page_limit": 10,
  "purpose": "Demonstrate enhanced government proposal capabilities and evaluation optimization",
  "evaluation_factors": ["technical_approach", "management_approach", "past_performance"],
  "sow_task_mapping": ["TASK 1: Technical Implementation", "TASK 2: Quality Assurance"],
  "compliance_requirements": ["FAR 52.215-1", "Section 508 Accessibility", "FISMA Security"],
  "success_metrics": ["100% SOW task completion", "Zero critical defects", "95% on-time delivery"],
  "risk_mitigation": ["Technical complexity: Phased approach", "Resource constraints: Backup staffing"],
  "rfp_vector_db_query": "Find comprehensive requirements for government proposal section",
  "client_vector_db_query": "Find government agency specific requirements and preferences",
  "custom_prompt": "Generate detailed content optimized for government evaluation criteria",
  "references": "Government acquisition regulations and best practices",
  "image_descriptions": ["Process flow diagram", "Compliance matrix"],
  "government_quality_score": 92  // High quality government-optimized score
}
'''
        
        print("📝 Simulated LLM response received (with comment)")
        
        # Test JSON extraction and cleaning
        outline = self.extract_and_clean_json(simulated_llm_response)
        
        if outline is None:
            raise ValueError(f"Failed to generate valid outline for section: {section_title}")
        
        # Add enhancement metadata
        enhanced_outline = {
            **outline,
            "enhancement_applied": True,
            "enhancement_timestamp": datetime.now().isoformat(),
            "enhancement_features": [
                "Government evaluation factor optimization",
                "Enhanced compliance integration", 
                "Quantitative success metrics",
                "Risk mitigation strategies",
                "JSON comment cleaning"
            ]
        }
        
        print(f"✅ Enhanced outline generated successfully!")
        print(f"   Quality Score: {enhanced_outline.get('government_quality_score', 0)}%")
        print(f"   Evaluation Factors: {len(enhanced_outline.get('evaluation_factors', []))}")
        print(f"   Success Metrics: {len(enhanced_outline.get('success_metrics', []))}")
        print(f"   Compliance Requirements: {len(enhanced_outline.get('compliance_requirements', []))}")
        
        return enhanced_outline

async def main():
    """Test the enhanced government outline generation"""
    
    print("🏛️  TESTING ENHANCED GOVERNMENT OUTLINE GENERATION")
    print("="*60)
    
    # Initialize service
    service = SimpleEnhancedGovernmentOutlineService()
    
    # Test with a sample section
    section_title = "Tab A - Proposal Cover/Transmittal Letter"
    section_desc = "Executive summary and proposal overview demonstrating understanding of requirements"
    
    try:
        result = await service.test_enhanced_outline_generation(section_title, section_desc)
        
        # Save result
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"test_enhanced_outline_{timestamp}.json"
        ProposalUtilities.save_json_to_file(result, output_filename)
        
        print(f"\n📄 Test results saved to: {output_filename}")
        print(f"🎉 Enhanced government outline generation test completed successfully!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
