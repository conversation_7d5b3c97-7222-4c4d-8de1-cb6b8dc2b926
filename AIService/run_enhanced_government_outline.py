#!/usr/bin/env python3
"""
Enhanced Government Proposal Outline Generator

This script demonstrates the improved government proposal outline generation with:
- Enhanced prompt engineering with FAR/DFARS compliance
- Improved context retrieval (12+ chunks vs 3)
- Government-specific quality metrics and validation
- Requirements traceability matrix generation
- Compliance cross-referencing
- No fallback mechanisms (fail fast with quality)

Usage: python run_enhanced_government_outline.py
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from services.proposal.enhanced_government_outline import EnhancedGovernmentOutlineService
from services.proposal.utilities import ProposalUtilities

def print_banner():
    """Print enhanced generator banner"""
    print("\n" + "="*80)
    print("🏛️  ENHANCED GOVERNMENT PROPOSAL OUTLINE GENERATOR")
    print("="*80)
    print("✨ Features:")
    print("   • Enhanced prompt engineering with government evaluation criteria")
    print("   • Improved context retrieval (12+ chunks vs 3)")
    print("   • Government-specific quality metrics and validation")
    print("   • Requirements traceability matrix generation")
    print("   • Compliance cross-referencing (FAR/DFARS/NIST)")
    print("   • No fallback mechanisms - quality or failure")
    print("="*80)

def print_comparison_metrics(result):
    """Print comparison with standard outline generation"""
    enhancement_summary = result.get("enhancement_summary", {})
    quality_metrics = result.get("government_quality_metrics", {})
    
    print("\n📊 ENHANCEMENT COMPARISON:")
    print("┌─────────────────────────────────┬──────────────┬──────────────┐")
    print("│ Metric                          │ Standard     │ Enhanced     │")
    print("├─────────────────────────────────┼──────────────┼──────────────┤")
    print(f"│ Context Chunks Retrieved        │ 3            │ 12+          │")
    print(f"│ Quality Scoring                 │ Basic        │ Government   │")
    print(f"│ Compliance Integration          │ Limited      │ Comprehensive│")
    print(f"│ Requirements Traceability       │ None         │ Full Matrix  │")
    print(f"│ Evaluation Factor Alignment     │ Generic      │ Specific     │")
    print(f"│ Government Terminology          │ Commercial   │ Acquisition  │")
    print(f"│ Overall Quality Score           │ N/A          │ {quality_metrics.get('overall_government_score', 0):.1f}%       │")
    print("└─────────────────────────────────┴──────────────┴──────────────┘")

def print_quality_breakdown(result):
    """Print detailed quality breakdown"""
    quality_metrics = result.get("government_quality_metrics", {})
    
    print("\n🎯 GOVERNMENT QUALITY METRICS:")
    print("┌─────────────────────────────────┬──────────────┐")
    print("│ Quality Dimension               │ Score        │")
    print("├─────────────────────────────────┼──────────────┤")
    print(f"│ Technical Depth Score           │ {quality_metrics.get('technical_depth_score', 0):>11.1f}% │")
    print(f"│ Compliance Score                │ {quality_metrics.get('compliance_score', 0):>11.1f}% │")
    print(f"│ Traceability Score              │ {quality_metrics.get('traceability_score', 0):>11.1f}% │")
    print(f"│ Evaluation Alignment Score      │ {quality_metrics.get('evaluation_alignment_score', 0):>11.1f}% │")
    print("├─────────────────────────────────┼──────────────┤")
    print(f"│ OVERALL GOVERNMENT SCORE        │ {quality_metrics.get('overall_government_score', 0):>11.1f}% │")
    print("└─────────────────────────────────┴──────────────┘")
    
    # Quality assessment
    overall_score = quality_metrics.get('overall_government_score', 0)
    if overall_score >= 90:
        grade = "A+ (Excellent - Highly Competitive)"
    elif overall_score >= 80:
        grade = "A  (Good - Competitive)"
    elif overall_score >= 70:
        grade = "B  (Acceptable - Needs Improvement)"
    else:
        grade = "C  (Poor - Significant Issues)"
    
    print(f"\n🏆 QUALITY GRADE: {grade}")

def print_enhancement_details(result):
    """Print enhancement details"""
    outlines = result.get("outlines", [])
    traceability_matrix = result.get("requirements_traceability_matrix", [])
    
    print(f"\n📋 ENHANCEMENT DETAILS:")
    print(f"   Total Sections Enhanced: {len(outlines)}")
    print(f"   Requirements Traced: {len(traceability_matrix)}")
    
    # Count enhanced features
    enhanced_features = {
        "evaluation_factors": 0,
        "sow_task_mapping": 0,
        "compliance_requirements": 0,
        "success_metrics": 0,
        "risk_mitigation": 0
    }
    
    for outline in outlines:
        for feature in enhanced_features:
            if outline.get(feature):
                enhanced_features[feature] += 1
    
    print(f"\n🔧 ENHANCED FEATURES APPLIED:")
    for feature, count in enhanced_features.items():
        feature_name = feature.replace('_', ' ').title()
        print(f"   {feature_name}: {count}/{len(outlines)} sections")

async def main():
    """Main execution function"""
    print_banner()
    
    # Configuration
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    
    print(f"\n📋 CONFIGURATION:")
    print(f"   Opportunity ID: {opportunity_id}")
    print(f"   Tenant ID: {tenant_id}")
    print(f"   Source: {source}")
    
    # Load table of contents
    try:
        print(f"\n📖 Loading table of contents...")
        table_of_contents_data = ProposalUtilities.read_json_from_file("table-of-contents.json")
        table_of_contents = table_of_contents_data.get("table_of_contents", [])
        print(f"   ✓ Loaded {len(table_of_contents)} sections")
        
        for i, section in enumerate(table_of_contents, 1):
            title = section.get("title", "Unknown")
            subsections = section.get("subsections", [])
            print(f"     {i}. {title} ({len(subsections)} subsections)")
            
    except FileNotFoundError:
        print("   ❌ table-of-contents.json not found!")
        print("   Make sure the file exists in the current directory")
        return 1
    except Exception as e:
        print(f"   ❌ Error loading table of contents: {e}")
        return 1
    
    # Initialize enhanced service
    print(f"\n🚀 Initializing Enhanced Government Outline Service...")
    try:
        enhanced_service = EnhancedGovernmentOutlineService(
            embedding_api_url="http://ai.kontratar.com:5000",
            llm_api_url="http://ai.kontratar.com:11434"
        )
        print("   ✓ Service initialized successfully")
    except Exception as e:
        print(f"   ❌ Failed to initialize service: {e}")
        return 1
    
    # Generate enhanced outline
    print(f"\n⚡ Generating enhanced government outline...")
    print("   This may take several minutes due to enhanced processing...")
    
    try:
        start_time = datetime.now()
        
        result = await enhanced_service.generate_enhanced_government_outline(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=table_of_contents
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"   ✓ Generation completed in {duration.total_seconds():.1f} seconds")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"enhanced_government_outline_{opportunity_id}_{timestamp}.json"
        ProposalUtilities.save_json_to_file(result, output_filename)
        print(f"   ✓ Results saved to: {output_filename}")
        
        # Print analysis
        print_comparison_metrics(result)
        print_quality_breakdown(result)
        print_enhancement_details(result)
        
        # Print critical gaps and recommendations
        quality_metrics = result.get("government_quality_metrics", {})
        critical_gaps = quality_metrics.get('critical_gaps', [])
        recommendations = quality_metrics.get('recommendations', [])
        
        if critical_gaps:
            print(f"\n⚠️  CRITICAL GAPS IDENTIFIED:")
            for gap in critical_gaps:
                print(f"   • {gap}")
        else:
            print(f"\n✅ NO CRITICAL GAPS IDENTIFIED")
        
        if recommendations:
            print(f"\n💡 RECOMMENDATIONS FOR IMPROVEMENT:")
            for rec in recommendations:
                print(f"   • {rec}")
        
        print(f"\n🎉 Enhanced Government Outline Generation Complete!")
        print(f"📄 Check {output_filename} for complete enhanced outline details.")
        
        return 0
        
    except Exception as e:
        print(f"   ❌ Generation failed: {e}")
        print(f"\n🔍 TROUBLESHOOTING:")
        print(f"   • Check network connectivity to AI services")
        print(f"   • Verify ChromaDB collections exist for opportunity")
        print(f"   • Ensure LLM service is available")
        print(f"   • Review logs for detailed error information")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print(f"\n⏹️  Generation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
